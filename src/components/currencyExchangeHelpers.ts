// Helper functions for CurrencyExchange
import { Currency } from '@/types/currency';

// Map currency names to appropriate flags
export const flagMap: { [key: string]: string } = {
  'toman': '🇮🇷',
  'lira': '🇹🇷',
  'dollar': '🇺🇸',
  // 'euro': '🇪🇺',
};

export function getCurrencyDisplayInfo(currencyName: string, currencies: Currency[]) {
  const currency = currencies.find(c => c.name === currencyName);
  if (currency) {
    return {
      name: `${currency.fa} (${currency.name.toUpperCase()})`,
      flag: flagMap[currency.name.toLowerCase()] || '💱',
    };
  }
  return {
    name: currencyName.toUpperCase(),
    flag: '💱',
  };
}

export function calculateExchangeRate(fromCurrency: string, toCurrency: string, currencies: Currency[]): number | null {
  const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
  const toCurrencyData = currencies.find(c => c.name === toCurrency);
  if (fromCurrencyData && toCurrencyData) {
    let fromRate: number, toRate: number;
    if (fromCurrency === 'toman') {
      fromRate = fromCurrencyData.sellPrice || 1;
    } else {
      fromRate = fromCurrencyData.sellPrice || 1;
    }
    if (toCurrency === 'toman') {
      toRate = toCurrencyData.buyPrice || 1;
    } else {
      toRate = toCurrencyData.sellPrice || 1;
    }
    if (fromRate > 0 && toRate > 0) {
      return fromRate / toRate;
    }
  }
  return null;
}
