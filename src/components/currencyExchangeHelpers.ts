// Helper functions for CurrencyExchange
import { Currency } from '@/types/currency';
import { CurrencyService } from '@/services/currencyService';

// Map currency names to appropriate flags
export const flagMap: { [key: string]: string } = {
  'toman': '🇮🇷',
  'lira': '🇹🇷',
  'dollar': '🇺🇸',
  'tether': '',
  // 'euro': '🇪🇺',
};

export function getCurrencyDisplayInfo(currencyName: string, currencies: Currency[]) {
  const currency = currencies.find(c => c.name === currencyName);
  if (currency) {
    return {
      name: `${currency.fa} (${currency.name.toUpperCase()})`,
      flag: flagMap[currency.name.toLowerCase()] || '💱',
    };
  }
  return {
    name: currencyName.toUpperCase(),
    flag: '💱',
  };
}

export function calculateExchangeRate(fromCurrency: string, toCurrency: string, currencies: Currency[]): number | null {
  const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
  const toCurrencyData = currencies.find(c => c.name === toCurrency);
  if (fromCurrencyData && toCurrencyData) {
    let fromRate: number, toRate: number;
    if (fromCurrency === 'toman') {
      fromRate = fromCurrencyData.sellPrice || 1;
    } else {
      fromRate = fromCurrencyData.sellPrice || 1;
    }
    if (toCurrency === 'toman') {
      toRate = toCurrencyData.buyPrice || 1;
    } else {
      toRate = toCurrencyData.sellPrice || 1;
    }
    if (fromRate > 0 && toRate > 0) {
      return fromRate / toRate;
    }
  }
  return null;
}

// Fetch currencies from API
export async function fetchCurrencies(): Promise<Currency[]> {
  try {
    const fetchedCurrencies = await CurrencyService.getCurrencies();
    return fetchedCurrencies;
  } catch (err) {
    throw new Error(err instanceof Error ? err.message : 'Failed to load currencies');
  }
}

// Currency conversion utility
export function convertCurrency(
  amount: string,
  fromCurrency: string,
  toCurrency: string,
  currencies: Currency[],
  reverse: boolean = false
): string {
  const value = amount.replace(/,/g, '');
  if (isNaN(Number(value))) return '0';

  const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
  const toCurrencyData = currencies.find(c => c.name === toCurrency);

  if (!fromCurrencyData || !toCurrencyData) return '0';

  let fromRate: number, toRate: number;
  if (fromCurrency === 'toman') {
    fromRate = fromCurrencyData.sellPrice || 1;
  } else {
    fromRate = fromCurrencyData.sellPrice || 1;
  }
  if (toCurrency === 'toman') {
    toRate = fromCurrencyData.buyPrice || 1;
  } else {
    toRate = toCurrencyData.sellPrice || 1;
  }

  if (fromRate <= 0 || toRate <= 0) return '0';

  const converted = reverse
    ? (Number(value) * toRate) / fromRate
    : (Number(value) * fromRate) / toRate;

  if (reverse && toCurrency !== 'toman') {
    // For reverse conversion to non-toman, use toLocaleString with formatting
    return Number.isInteger(converted)
      ? converted.toLocaleString()
      : converted.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  } else {
    // For forward conversion or to toman, use simple formatting
    return Number.isInteger(converted) ? String(converted) : converted.toFixed(2);
  }
}

// Format amount with proper locale formatting
export function formatAmount(value: string): string {
  const numValue = value.replace(/,/g, '');
  if (!isNaN(Number(numValue))) {
    return Number(numValue).toLocaleString();
  }
  return value;
}

// Get default currencies for initialization
export function getDefaultCurrencies(currencies: Currency[]): { from: string; to: string } {
  if (currencies.length === 0) return { from: 'toman', to: 'toman' };

  const tomanCurrency = currencies.find(c => c.name === 'toman');
  const liraCurrency = currencies.find(c => c.name === 'lira');

  if (tomanCurrency && liraCurrency) {
    return { from: tomanCurrency.name, to: liraCurrency.name };
  } else if (currencies.length >= 2) {
    return { from: currencies[0].name, to: currencies[1].name };
  }

  return { from: 'toman', to: 'toman' };
}
