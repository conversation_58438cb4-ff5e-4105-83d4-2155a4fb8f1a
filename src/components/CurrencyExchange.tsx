'use client';

import { useState, useEffect } from 'react';
import styles from './CurrencyExchange.module.css';
import { Currency } from '@/types/currency';
import { CurrencyService } from '@/services/currencyService';
import { getCurrencyDisplayInfo, calculateExchangeRate } from './currencyExchangeHelpers';

export default function CurrencyExchange() {
  const [fromAmount, setFromAmount] = useState('1,000,000');
  const [toAmount, setToAmount] = useState('0');
  const [fromCurrency, setFromCurrency] = useState('toman');
  const [toCurrency, setToCurrency] = useState('toman');
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fromDropdownOpen, setFromDropdownOpen] = useState(false);
  const [toDropdownOpen, setToDropdownOpen] = useState(false);

  // Fetch currencies on component mount
  useEffect(() => {
    const fetchCurrencies = async () => {
      try {
        setLoading(true);
        setError(null);
        const fetchedCurrencies = await CurrencyService.getCurrencies();
        setCurrencies(fetchedCurrencies);

        // Set default currencies if available
        if (fetchedCurrencies.length > 0) {
          const tomanCurrency = fetchedCurrencies.find(c => c.name === 'toman');
          const liraCurrency = fetchedCurrencies.find(c => c.name === 'lira');

          if (tomanCurrency && liraCurrency) {
            setFromCurrency(tomanCurrency.name);
            setToCurrency(liraCurrency.name);
          } else if (fetchedCurrencies.length >= 2) {
            setFromCurrency(fetchedCurrencies[0].name);
            setToCurrency(fetchedCurrencies[1].name);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load currencies');
        console.error('Error fetching currencies:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrencies();
  }, []);

  const handleSwapCurrencies = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
    setFromAmount(toAmount);
    setToAmount(fromAmount);
  };

  // Dropdown handlers
  const handleFromDropdownToggle = () => {
    setFromDropdownOpen(!fromDropdownOpen);
    setToDropdownOpen(false);
  };

  const handleToDropdownToggle = () => {
    setToDropdownOpen(!toDropdownOpen);
    setFromDropdownOpen(false);
  };

  const handleFromCurrencySelect = (currencyName: string) => {
    setFromCurrency(currencyName);
    setFromDropdownOpen(false);
  };

  const handleToCurrencySelect = (currencyName: string) => {
    setToCurrency(currencyName);
    setToDropdownOpen(false);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown-container')) {
        setFromDropdownOpen(false);
        setToDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleFromAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/,/g, '');
    if (!isNaN(Number(value))) {
      setFromAmount(Number(value).toLocaleString());

      // Calculate conversion based on currency rates
      const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
      const toCurrencyData = currencies.find(c => c.name === toCurrency);

      if (fromCurrencyData && toCurrencyData) {
        // toman logic: use sellPrice if in from, buyPrice if in to
        let fromRate: number, toRate: number;
        if (fromCurrency === 'toman') {
          fromRate = fromCurrencyData.sellPrice || 1;
        } else {
          fromRate = fromCurrencyData.sellPrice || 1;
        }
        if (toCurrency === 'toman') {
          toRate = fromCurrencyData.buyPrice || 1;
        } else {
          toRate = toCurrencyData.sellPrice || 1;
        }

        let convertedAmount: string;
        if (fromRate > 0 && toRate > 0) {
          const converted = (Number(value) * fromRate) / toRate;
          // Remove .00 if integer
          convertedAmount = Number.isInteger(converted) ? String(converted) : converted.toFixed(2);
        } else {
          convertedAmount = '0';
        }
        setToAmount(convertedAmount);
      } else {
        setToAmount('0');
      }
    }
  };

  const handleToAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/,/g, '');
    if (!isNaN(Number(value))) {
      setToAmount(value);

      // Calculate reverse conversion based on currency rates
      const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
      const toCurrencyData = currencies.find(c => c.name === toCurrency);

      if (fromCurrencyData && toCurrencyData) {
        // toman logic: use sellPrice if in from, buyPrice if in to
        let fromRate: number, toRate: number;
        if (fromCurrency === 'toman') {
          fromRate = fromCurrencyData.sellPrice || 1;
        } else {
          fromRate = fromCurrencyData.sellPrice || 1;
        }
        if (toCurrency === 'toman') {
          toRate = toCurrencyData.buyPrice || 1;
        } else {
          toRate = toCurrencyData.sellPrice || 1;
        }

        let convertedAmount: string;
        if (fromRate > 0 && toRate > 0) {
          const converted = (Number(value) * toRate) / fromRate;
          // Remove .00 if integer
          convertedAmount = Number.isInteger(converted) ? converted.toLocaleString() : converted.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        } else {
          convertedAmount = '0';
        }
        setFromAmount(convertedAmount);
      } else {
        setFromAmount('0');
      }
    }
  };

  // Recalculate toAmount when fromCurrency or toCurrency changes
  useEffect(() => {
    const value = fromAmount.replace(/,/g, '');
    if (!isNaN(Number(value))) {
      const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
      const toCurrencyData = currencies.find(c => c.name === toCurrency);
      if (fromCurrencyData && toCurrencyData) {
        let fromRate: number, toRate: number;
        if (fromCurrency === 'toman') {
          fromRate = fromCurrencyData.sellPrice || 1;
        } else {
          fromRate = fromCurrencyData.sellPrice || 1;
        }
        if (toCurrency === 'toman') {
          toRate = fromCurrencyData.buyPrice || 1;
        } else {
          toRate = toCurrencyData.sellPrice || 1;
        }
        let convertedAmount: string;
        if (fromRate > 0 && toRate > 0) {
          const converted = (Number(value) * fromRate) / toRate;
          // Remove .00 if integer
          convertedAmount = Number.isInteger(converted) ? String(converted) : converted.toFixed(2);
        } else {
          convertedAmount = '0';
        }
        setToAmount(convertedAmount);
      } else {
        setToAmount('0');
      }
    }
  }, [fromCurrency, toCurrency, currencies]);

  // Show loading state
  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.exchangeCard}>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>در حال بارگذاری ارزها...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.exchangeCard}>
          <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
            <p>خطا در بارگذاری ارزها: {error}</p>
            <button
              onClick={() => window.location.reload()}
              style={{ marginTop: '1rem', padding: '0.5rem 1rem' }}
            >
              تلاش مجدد
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.exchangeCard}>
        {/* From Currency */}
        <div className={`${styles.currencySection} dropdown-container`}>
          <div className={styles.currencyHeader}>
            <div className={styles.currencyInfo}>
              <span className={styles.currencyName}>
                {getCurrencyDisplayInfo(fromCurrency, currencies).name}
              </span>
              <div className={styles.flag}>
                {getCurrencyDisplayInfo(fromCurrency, currencies).flag}
              </div>
            </div>
            <button
              className={styles.dropdownBtn}
              onClick={handleFromDropdownToggle}
            >
              {fromDropdownOpen ? '▲' : '▼'}
            </button>
          </div>
          {fromDropdownOpen && (
            <div className={styles.dropdown}>
              {fromCurrency === 'toman'
                ? currencies
                    .filter(currency => currency.name === 'toman')
                    .map((currency) => (
                      <div
                        key={currency._id}
                        className={`${styles.dropdownItem} ${currency.name === fromCurrency ? styles.dropdownItemActive : ''}`}
                        onClick={() => handleFromCurrencySelect(currency.name)}
                      >
                        <div className={styles.dropdownItemInfo}>
                          <span className={styles.dropdownItemFlag}>
                            {(() => {
                              const flagMap: { [key: string]: string } = {
                                'toman': '🇮🇷',
                                'lira': '🇹🇷',
                                'dollar': '🇺🇸',
                              };
                              return flagMap[currency.name.toLowerCase()] || '💱';
                            })()}
                          </span>
                          <span className={styles.dropdownItemName}>
                            {currency.fa} ({currency.name.toUpperCase()})
                          </span>
                        </div>
                      </div>
                    ))
                : currencies
                    .filter(currency => currency.name !== 'toman')
                    .map((currency) => (
                      <div
                        key={currency._id}
                        className={`${styles.dropdownItem} ${currency.name === fromCurrency ? styles.dropdownItemActive : ''}`}
                        onClick={() => handleFromCurrencySelect(currency.name)}
                      >
                        <div className={styles.dropdownItemInfo}>
                          <span className={styles.dropdownItemFlag}>
                            {(() => {
                              const flagMap: { [key: string]: string } = {
                                'toman': '🇮🇷',
                                'lira': '🇹🇷',
                                'dollar': '🇺🇸',
                              };
                              return flagMap[currency.name.toLowerCase()] || '💱';
                            })()}
                          </span>
                          <span className={styles.dropdownItemName}>
                            {currency.fa} ({currency.name.toUpperCase()})
                          </span>
                        </div>
                      </div>
                    ))}
            </div>
          )}
          <div className={styles.amountSection}>
            <input
              type="text"
              value={fromAmount}
              onChange={handleFromAmountChange}
              className={styles.amountInput}
            />
            <span className={`${styles.amountLabel} fa-text`}>پرداخت می کنید</span>
          </div>
        </div>

        {/* Swap Button */}
        <div className={styles.swapSection}>
          <button 
            className={styles.swapBtn}
            onClick={handleSwapCurrencies}
          >
            ⇅
          </button>
        </div>

        {/* To Currency */}
        <div className={`${styles.currencySection} dropdown-container`}>
          <div className={styles.currencyHeader}>
            <div className={styles.currencyInfo}>
              <span className={styles.currencyName}>
                {getCurrencyDisplayInfo(toCurrency, currencies).name}
              </span>
              <div className={styles.flag}>
                {getCurrencyDisplayInfo(toCurrency, currencies).flag}
              </div>
            </div>
            <button
              className={styles.dropdownBtn}
              onClick={handleToDropdownToggle}
            >
              {toDropdownOpen ? '▲' : '▼'}
            </button>
          </div>
          {toDropdownOpen && (
            <div className={styles.dropdown}>
              {fromCurrency != 'toman'
                ? currencies
                    .filter(currency => currency.name === 'toman')
                    .map((currency) => (
                      <div
                        key={currency._id}
                        className={`${styles.dropdownItem} ${currency.name === toCurrency ? styles.dropdownItemActive : ''}`}
                        onClick={() => handleToCurrencySelect(currency.name)}
                      >
                        <div className={styles.dropdownItemInfo}>
                          <span className={styles.dropdownItemFlag}>
                            {(() => {
                              const flagMap: { [key: string]: string } = {
                                'toman': '🇮🇷',
                                'lira': '🇹🇷',
                                'dollar': '🇺🇸',
                                'tether': '',
                              };
                              return flagMap[currency.name.toLowerCase()] || '💱';
                            })()}
                          </span>
                          <span className={styles.dropdownItemName}>
                            {currency.fa} ({currency.name.toUpperCase()})
                          </span>
                        </div>
                      </div>
                    ))
                : currencies
                    .filter(currency => currency.name !== 'toman')
                    .map((currency) => (
                      <div
                        key={currency._id}
                        className={`${styles.dropdownItem} ${currency.name === toCurrency ? styles.dropdownItemActive : ''}`}
                        onClick={() => handleToCurrencySelect(currency.name)}
                      >
                        <div className={styles.dropdownItemInfo}>
                          <span className={styles.dropdownItemFlag}>
                            {(() => {
                              const flagMap: { [key: string]: string } = {
                                'toman': '🇮🇷',
                                'lira': '🇹🇷',
                                'dollar': '🇺🇸',
                                'tether': '',
                              };
                              return flagMap[currency.name.toLowerCase()] || '💱';
                            })()}
                          </span>
                          <span className={styles.dropdownItemName}>
                            {currency.fa} ({currency.name.toUpperCase()})
                          </span>
                        </div>
                      </div>
                    ))}
            </div>
          )}
          <div className={styles.amountSection}>
            <input
              type="text"
              value={toAmount}
              onChange={handleToAmountChange}
              className={styles.amountInput}
            />
            <span className={`${styles.amountLabel} fa-text`}>دریافت می کنید</span>
          </div>
        </div>

        {/* Exchange Rate */}
        <div className={styles.exchangeRate + ' fa-text'}>
          {(() => {
            const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
            const toCurrencyData = currencies.find(c => c.name === toCurrency);

            if (fromCurrencyData && toCurrencyData) {
              const rate = calculateExchangeRate(fromCurrency, toCurrency, currencies);
              if (rate !== null) {
                return `1 ${fromCurrencyData.fa} = ${rate.toFixed(4)} ${toCurrencyData.fa}`;
              }
            }

            return 'نرخ تبدیل در دسترس نیست';
          })()}
        </div>

        {/* Start Transaction Button */}
        <button
          className={styles.startBtn + ' fa-text'}
          onClick={() => window.location.href = '/order'}
        >
           ثبت سفارش
        </button>
      </div>
    </div>
  );
}
