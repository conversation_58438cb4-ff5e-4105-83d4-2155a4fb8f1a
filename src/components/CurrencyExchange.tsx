'use client';

import { useState, useEffect } from 'react';
import styles from './CurrencyExchange.module.css';
import { Currency } from '@/types/currency';
import {
  getCurrencyDisplayInfo,
  calculateExchangeRate,
  fetchCurrencies,
  convertCurrency,
  getDefaultCurrencies,
  createSwapHandler,
  createDropdownToggleHandler,
  createCurrencySelectHandler,
  createAmountChangeHandler,
  getFilteredCurrencies,
  createClickOutsideHandler,
  flagMap
} from './currencyExchangeHelpers';

export default function CurrencyExchange() {
  const [fromAmount, setFromAmount] = useState('1,000,000');
  const [toAmount, setToAmount] = useState('0');
  const [fromCurrency, setFromCurrency] = useState('toman');
  const [toCurrency, setToCurrency] = useState('toman');
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fromDropdownOpen, setFromDropdownOpen] = useState(false);
  const [toDropdownOpen, setToDropdownOpen] = useState(false);

  // Fetch currencies on component mount
  useEffect(() => {
    const loadCurrencies = async () => {
      try {
        setLoading(true);
        setError(null);
        const fetchedCurrencies = await fetchCurrencies();
        setCurrencies(fetchedCurrencies);

        // Set default currencies if available
        const defaultCurrencies = getDefaultCurrencies(fetchedCurrencies);
        setFromCurrency(defaultCurrencies.from);
        setToCurrency(defaultCurrencies.to);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load currencies');
        console.error('Error fetching currencies:', err);
      } finally {
        setLoading(false);
      }
    };

    loadCurrencies();
  }, []);

  // Event handlers using helper functions
  const handleSwapCurrencies = createSwapHandler(
    fromCurrency, toCurrency, fromAmount, toAmount,
    setFromCurrency, setToCurrency, setFromAmount, setToAmount
  );

  const handleFromDropdownToggle = createDropdownToggleHandler(
    fromDropdownOpen, setFromDropdownOpen, setToDropdownOpen
  );

  const handleToDropdownToggle = createDropdownToggleHandler(
    toDropdownOpen, setToDropdownOpen, setFromDropdownOpen
  );

  const handleFromCurrencySelect = createCurrencySelectHandler(
    setFromCurrency, setFromDropdownOpen
  );

  const handleToCurrencySelect = createCurrencySelectHandler(
    setToCurrency, setToDropdownOpen
  );

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = createClickOutsideHandler(setFromDropdownOpen, setToDropdownOpen);
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleFromAmountChange = createAmountChangeHandler(
    fromCurrency, toCurrency, currencies, setFromAmount, setToAmount, false
  );

  const handleToAmountChange = createAmountChangeHandler(
    fromCurrency, toCurrency, currencies, setFromAmount, setToAmount, true
  );

  // Recalculate toAmount when fromCurrency or toCurrency changes
  useEffect(() => {
    const value = fromAmount.replace(/,/g, '');
    if (!isNaN(Number(value)) && currencies.length > 0) {
      const convertedAmount = convertCurrency(value, fromCurrency, toCurrency, currencies, false);
      setToAmount(convertedAmount);
    }
  }, [fromCurrency, toCurrency, currencies, fromAmount]);

  // Show loading state
  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.exchangeCard}>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>در حال بارگذاری ارزها...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.exchangeCard}>
          <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
            <p>خطا در بارگذاری ارزها: {error}</p>
            <button
              onClick={() => window.location.reload()}
              style={{ marginTop: '1rem', padding: '0.5rem 1rem' }}
            >
              تلاش مجدد
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.exchangeCard}>
        {/* From Currency */}
        <div className={`${styles.currencySection} dropdown-container`}>
          <div className={styles.currencyHeader}>
            <div className={styles.currencyInfo}>
              <span className={styles.currencyName}>
                {getCurrencyDisplayInfo(fromCurrency, currencies).name}
              </span>
              <div className={styles.flag}>
                {getCurrencyDisplayInfo(fromCurrency, currencies).flag}
              </div>
            </div>
            <button
              className={styles.dropdownBtn}
              onClick={handleFromDropdownToggle}
            >
              {fromDropdownOpen ? '▲' : '▼'}
            </button>
          </div>
          {fromDropdownOpen && (
            <div className={styles.dropdown}>
              {getFilteredCurrencies(currencies, fromCurrency, true).map((currency) => (
                <div
                  key={currency._id}
                  className={`${styles.dropdownItem} ${currency.name === fromCurrency ? styles.dropdownItemActive : ''}`}
                  onClick={() => handleFromCurrencySelect(currency.name)}
                >
                  <div className={styles.dropdownItemInfo}>
                    <span className={styles.dropdownItemFlag}>
                      {flagMap[currency.name.toLowerCase()] || '💱'}
                    </span>
                    <span className={styles.dropdownItemName}>
                      {currency.fa} ({currency.name.toUpperCase()})
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
          <div className={styles.amountSection}>
            <input
              type="text"
              value={fromAmount}
              onChange={handleFromAmountChange}
              className={styles.amountInput}
            />
            <span className={`${styles.amountLabel} fa-text`}>پرداخت می کنید</span>
          </div>
        </div>

        {/* Swap Button */}
        <div className={styles.swapSection}>
          <button 
            className={styles.swapBtn}
            onClick={handleSwapCurrencies}
          >
            ⇅
          </button>
        </div>

        {/* To Currency */}
        <div className={`${styles.currencySection} dropdown-container`}>
          <div className={styles.currencyHeader}>
            <div className={styles.currencyInfo}>
              <span className={styles.currencyName}>
                {getCurrencyDisplayInfo(toCurrency, currencies).name}
              </span>
              <div className={styles.flag}>
                {getCurrencyDisplayInfo(toCurrency, currencies).flag}
              </div>
            </div>
            <button
              className={styles.dropdownBtn}
              onClick={handleToDropdownToggle}
            >
              {toDropdownOpen ? '▲' : '▼'}
            </button>
          </div>
          {toDropdownOpen && (
            <div className={styles.dropdown}>
              {getFilteredCurrencies(currencies, fromCurrency, false).map((currency) => (
                <div
                  key={currency._id}
                  className={`${styles.dropdownItem} ${currency.name === toCurrency ? styles.dropdownItemActive : ''}`}
                  onClick={() => handleToCurrencySelect(currency.name)}
                >
                  <div className={styles.dropdownItemInfo}>
                    <span className={styles.dropdownItemFlag}>
                      {flagMap[currency.name.toLowerCase()] || '💱'}
                    </span>
                    <span className={styles.dropdownItemName}>
                      {currency.fa} ({currency.name.toUpperCase()})
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
          <div className={styles.amountSection}>
            <input
              type="text"
              value={toAmount}
              onChange={handleToAmountChange}
              className={styles.amountInput}
            />
            <span className={`${styles.amountLabel} fa-text`}>دریافت می کنید</span>
          </div>
        </div>

        {/* Exchange Rate */}
        <div className={styles.exchangeRate + ' fa-text'}>
          {(() => {
            const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
            const toCurrencyData = currencies.find(c => c.name === toCurrency);

            if (fromCurrencyData && toCurrencyData) {
              const rate = calculateExchangeRate(fromCurrency, toCurrency, currencies);
              if (rate !== null) {
                return `1 ${fromCurrencyData.fa} = ${rate.toFixed(4)} ${toCurrencyData.fa}`;
              }
            }

            return 'نرخ تبدیل در دسترس نیست';
          })()}
        </div>

        {/* Start Transaction Button */}
        <button
          className={styles.startBtn + ' fa-text'}
          onClick={() => window.location.href = '/order'}
        >
           ثبت سفارش
        </button>
      </div>
    </div>
  );
}
