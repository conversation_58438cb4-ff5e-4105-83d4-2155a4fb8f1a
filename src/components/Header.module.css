.header {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  color: #d32f2f;
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

.nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav a {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.nav a:hover {
  color: #d32f2f;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .nav ul {
    gap: 1rem;
  }
  
  .nav a {
    font-size: 0.9rem;
  }
  
  .logo h1 {
    font-size: 1.5rem;
  }
}

@media (prefers-color-scheme: dark) {
  .header {
    background: #1a1a1a;
    border-bottom-color: #333;
  }
  
  .nav a {
    color: #e5e5e5;
  }
  
  .nav a:hover {
    color: #ff6b6b;
  }
}
